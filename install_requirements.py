#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖安装脚本
自动安装HTML上传工具所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 HTML上传工具依赖安装程序")
    print("=" * 50)
    
    # 需要安装的包
    packages = [
        "paramiko",  # SSH/SFTP客户端
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("✅ 所有依赖安装成功！现在可以使用HTML上传工具了。")
        print("\n使用方法:")
        print("python html_uploader.py your_file.html")
        print("python html_uploader.py -p project_name your_file.html")
        print("python html_uploader.py your_directory/")
    else:
        print("❌ 部分依赖安装失败，请检查网络连接或手动安装。")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
