HTML文件SSH上传工具 - 使用说明
=====================================

📋 工具简介
-----------
这是一个专门用于将HTML文件上传到2号服务器的Python工具包。
支持SSH/SFTP方式上传，自动生成访问链接，操作简单快捷。

🎯 服务器信息
-------------
服务器：2号服务器（说明文档和工具类）
主机：39.97.189.146
上传路径：/shuomingwendang/
访问地址：http://www.xli8.com/shuomingwendang/

📦 文件说明
-----------
html_uploader.py      - 主要上传工具（命令行版本）
quick_upload.py       - 快速上传工具（交互式版本）
install_requirements.py - 依赖安装脚本
upload.bat           - Windows批处理脚本
test_example.html    - 测试用HTML文件
README.md           - 详细说明文档
使用说明.txt         - 本文件

🚀 快速开始
-----------

1. 安装依赖
   双击运行：install_requirements.py
   或命令行：python install_requirements.py

2. 快速上传（推荐新手）
   双击运行：quick_upload.py
   按照提示操作即可

3. 命令行上传（推荐高级用户）
   python html_uploader.py your_file.html
   python html_uploader.py -p project_name your_file.html

💡 使用示例
-----------

示例1：上传单个文件
python html_uploader.py test_example.html

示例2：指定项目名称
python html_uploader.py -p "my_project" test_example.html

示例3：批量上传目录
python html_uploader.py ./html_files/

示例4：详细输出
python html_uploader.py -v test_example.html

📝 文件命名规范
---------------
上传的文件会自动重命名为：项目名称-时间戳.html
例如：my_project-1691234567.html

🔧 常见问题
-----------

Q: 提示"SSH连接失败"怎么办？
A: 检查网络连接，确保能访问 39.97.189.146

Q: 提示"ModuleNotFoundError: No module named 'paramiko'"？
A: 运行 install_requirements.py 安装依赖

Q: 上传后如何访问文件？
A: 使用生成的访问链接，格式为：
   http://www.xli8.com/shuomingwendang/文件名.html

Q: 可以上传哪些文件？
A: 只支持 .html 和 .htm 文件

Q: 如何批量上传？
A: 将所有HTML文件放在一个文件夹中，然后：
   python html_uploader.py ./文件夹路径/

📞 技术支持
-----------
如遇问题，请检查：
1. upload.log 日志文件
2. 网络连接状态
3. 服务器访问权限

作者：项亮002
版本：v1.0
更新时间：2025-08-01
