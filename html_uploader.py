#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML文件SSH上传工具
自动上传HTML文件到2号服务器（说明文档和工具类）
作者：项亮002
版本：v1.0
"""

import os
import sys
import time
import paramiko
import argparse
from pathlib import Path
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('upload.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HTMLUploader:
    """HTML文件上传器"""
    
    def __init__(self):
        # 2号服务器配置
        self.host = "*************"
        self.port = 22  # SSH端口
        self.username = "root"
        self.password = "Zxczxczxc111."
        self.remote_base_path = "/shuomingwendang/"
        self.domain = "www.xli8.com"
        self.access_url_base = f"http://{self.domain}/shuomingwendang/"
        
        # 连接对象
        self.ssh = None
        self.sftp = None
    
    def connect(self):
        """建立SSH连接"""
        try:
            logger.info(f"正在连接到服务器 {self.host}:{self.port}")
            
            # 创建SSH客户端
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 建立连接
            self.ssh.connect(
                hostname=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                timeout=30
            )
            
            # 创建SFTP会话
            self.sftp = self.ssh.open_sftp()
            
            logger.info("SSH连接建立成功")
            return True
            
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            return False
    
    def disconnect(self):
        """关闭连接"""
        try:
            if self.sftp:
                self.sftp.close()
                logger.info("SFTP会话已关闭")
            
            if self.ssh:
                self.ssh.close()
                logger.info("SSH连接已关闭")
                
        except Exception as e:
            logger.error(f"关闭连接时出错: {e}")
    
    def ensure_remote_directory(self, remote_path):
        """确保远程目录存在"""
        try:
            # 尝试切换到目录
            self.sftp.chdir(remote_path)
            logger.info(f"远程目录已存在: {remote_path}")
            return True

        except FileNotFoundError:
            try:
                # 目录不存在，尝试创建多级目录
                logger.info(f"创建远程目录: {remote_path}")

                # 分解路径，逐级创建
                path_parts = remote_path.strip('/').split('/')
                current_path = '/'

                for part in path_parts:
                    if part:  # 跳过空字符串
                        current_path = current_path.rstrip('/') + '/' + part
                        try:
                            self.sftp.chdir(current_path)
                            logger.info(f"目录已存在: {current_path}")
                        except FileNotFoundError:
                            logger.info(f"创建目录: {current_path}")
                            self.sftp.mkdir(current_path)
                            self.sftp.chmod(current_path, 0o755)

                return True

            except Exception as e:
                logger.error(f"创建远程目录失败: {e}")
                return False

        except Exception as e:
            logger.error(f"检查远程目录时出错: {e}")
            return False
    
    def generate_filename(self, project_name, original_filename):
        """生成符合规范的文件名：项目名称的拼音-当前时间戳.html"""
        timestamp = str(int(time.time()))
        
        # 如果原文件名已经是HTML，保持扩展名
        if original_filename.lower().endswith('.html'):
            filename = f"{project_name}-{timestamp}.html"
        else:
            filename = f"{project_name}-{timestamp}.html"
        
        return filename
    
    def upload_file(self, local_file_path, project_name=None):
        """上传单个文件"""
        try:
            local_path = Path(local_file_path)
            
            if not local_path.exists():
                logger.error(f"本地文件不存在: {local_file_path}")
                return False
            
            if not local_path.is_file():
                logger.error(f"路径不是文件: {local_file_path}")
                return False
            
            # 生成项目名称（如果未提供）
            if not project_name:
                project_name = local_path.stem
            
            # 生成远程文件名
            remote_filename = self.generate_filename(project_name, local_path.name)
            remote_file_path = self.remote_base_path + remote_filename
            
            logger.info(f"开始上传文件:")
            logger.info(f"  本地文件: {local_file_path}")
            logger.info(f"  远程文件: {remote_file_path}")
            
            # 确保远程目录存在
            if not self.ensure_remote_directory(self.remote_base_path):
                return False
            
            # 上传文件
            self.sftp.put(str(local_path), remote_file_path)
            
            # 设置文件权限
            self.sftp.chmod(remote_file_path, 0o644)
            
            # 生成访问链接
            access_url = self.access_url_base + remote_filename
            
            logger.info("文件上传成功!")
            logger.info(f"访问链接: {access_url}")
            
            return {
                'success': True,
                'local_file': str(local_path),
                'remote_file': remote_file_path,
                'filename': remote_filename,
                'access_url': access_url
            }
            
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            return False
    
    def upload_directory(self, local_dir_path, project_name=None):
        """上传目录中的所有HTML文件"""
        try:
            local_dir = Path(local_dir_path)
            
            if not local_dir.exists():
                logger.error(f"本地目录不存在: {local_dir_path}")
                return False
            
            if not local_dir.is_dir():
                logger.error(f"路径不是目录: {local_dir_path}")
                return False
            
            # 查找所有HTML文件
            html_files = list(local_dir.glob("*.html")) + list(local_dir.glob("*.htm"))
            
            if not html_files:
                logger.warning(f"目录中没有找到HTML文件: {local_dir_path}")
                return False
            
            logger.info(f"找到 {len(html_files)} 个HTML文件")
            
            results = []
            for html_file in html_files:
                # 使用文件名作为项目名称（如果未提供）
                file_project_name = project_name or html_file.stem
                result = self.upload_file(html_file, file_project_name)
                if result:
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"上传目录失败: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='HTML文件SSH上传工具')
    parser.add_argument('path', help='要上传的文件或目录路径')
    parser.add_argument('-p', '--project', help='项目名称（用于生成文件名）')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建上传器
    uploader = HTMLUploader()
    
    try:
        # 建立连接
        if not uploader.connect():
            logger.error("无法连接到服务器")
            sys.exit(1)
        
        # 判断是文件还是目录
        path = Path(args.path)
        
        if path.is_file():
            # 上传单个文件
            result = uploader.upload_file(args.path, args.project)
            if result:
                print(f"\n✅ 上传成功!")
                print(f"📁 文件名: {result['filename']}")
                print(f"🌐 访问链接: {result['access_url']}")
            else:
                print("❌ 上传失败")
                sys.exit(1)
                
        elif path.is_dir():
            # 上传目录
            results = uploader.upload_directory(args.path, args.project)
            if results:
                print(f"\n✅ 批量上传成功! 共上传 {len(results)} 个文件:")
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result['filename']} -> {result['access_url']}")
            else:
                print("❌ 批量上传失败")
                sys.exit(1)
        else:
            logger.error(f"路径不存在: {args.path}")
            sys.exit(1)
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        sys.exit(1)
    
    finally:
        # 关闭连接
        uploader.disconnect()

if __name__ == "__main__":
    main()
