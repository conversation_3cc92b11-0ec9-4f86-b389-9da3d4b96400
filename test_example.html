<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试文档 - 项亮002</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        .info-box {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 30px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 HTML上传工具测试文档</h1>
        
        <div class="info-box success">
            <h3>上传成功！</h3>
            <p>如果您能看到这个页面，说明HTML上传工具工作正常。</p>
        </div>
        
        <div class="info-box">
            <h3>📋 工具特性</h3>
            <ul class="feature-list">
                <li>全自动SSH连接</li>
                <li>无需手动输入密码</li>
                <li>支持文件和目录上传</li>
                <li>自动生成访问链接</li>
                <li>错误处理和重试机制</li>
                <li>自动文件命名规范</li>
            </ul>
        </div>
        
        <div class="info-box">
            <h3>🎯 服务器信息</h3>
            <p><strong>主机：</strong> 39.97.189.146</p>
            <p><strong>上传路径：</strong> /var/www/html/shuomingwendang/</p>
            <p><strong>访问域名：</strong> www.xli8.com</p>
        </div>
        
        <div class="info-box">
            <h3>💡 使用示例</h3>
            <pre><code># 上传单个文件
python html_uploader.py -p "test_doc" test_example.html

# 批量上传
python html_uploader.py -p "docs" ./html_files/

# Windows用户
upload.bat -p "test_doc" test_example.html</code></pre>
        </div>
        
        <div class="timestamp">
            <p>📅 生成时间：<span id="timestamp"></span></p>
            <p>👨‍💻 作者：项亮002</p>
            <p>🔧 版本：v1.0</p>
        </div>
    </div>
    
    <script>
        // 显示当前时间
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html>
