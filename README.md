# HTML文件SSH上传工具

## 📋 功能介绍

这是一个专门用于将HTML文件上传到2号服务器的Python工具，支持：

- ✅ 全自动SSH连接
- ✅ 无需手动输入密码
- ✅ 支持文件和目录上传
- ✅ 自动生成访问链接
- ✅ 错误处理和重试机制
- ✅ 自动文件命名规范

## 🎯 服务器信息

**2号服务器（说明文档和工具类）**
- 主机：*************
- SSH端口：22
- 用户名：root
- 密码：Zxczxczxc111.
- 上传路径：/var/www/html/shuomingwendang/
- 访问地址：http://www.xli8.com/shuomingwendang/

## 📦 安装依赖

### 方法1：自动安装（推荐）
```bash
python install_requirements.py
```

### 方法2：手动安装
```bash
pip install paramiko
```

## 🚀 使用方法

### 1. 上传单个HTML文件
```bash
# 基本用法
python html_uploader.py your_file.html

# 指定项目名称
python html_uploader.py -p my_project your_file.html

# 详细输出
python html_uploader.py -v your_file.html
```

### 2. 批量上传目录中的HTML文件
```bash
# 上传目录中所有HTML文件
python html_uploader.py your_directory/

# 指定项目名称前缀
python html_uploader.py -p project_name your_directory/
```

## 📝 文件命名规范

上传的文件会自动按照以下规范重命名：
```
项目名称的拼音-当前时间戳.html
```

**示例：**
- 原文件：`index.html`
- 项目名称：`my_project`
- 生成文件名：`my_project-1691234567.html`

## 💡 使用示例

### 示例1：上传单个文件
```bash
python html_uploader.py -p "document_guide" guide.html
```
**输出：**
```
✅ 上传成功!
📁 文件名: document_guide-1691234567.html
🌐 访问链接: http://www.xli8.com/shuomingwendang/document_guide-1691234567.html
```

### 示例2：批量上传
```bash
python html_uploader.py -p "tool_docs" ./html_files/
```
**输出：**
```
✅ 批量上传成功! 共上传 3 个文件:
1. tool_docs-1691234567.html -> http://www.xli8.com/shuomingwendang/tool_docs-1691234567.html
2. tool_docs-1691234568.html -> http://www.xli8.com/shuomingwendang/tool_docs-1691234568.html
3. tool_docs-1691234569.html -> http://www.xli8.com/shuomingwendang/tool_docs-1691234569.html
```

## 🔧 命令行参数

| 参数 | 简写 | 说明 | 必需 |
|------|------|------|------|
| `path` | - | 要上传的文件或目录路径 | ✅ |
| `--project` | `-p` | 项目名称（用于生成文件名） | ❌ |
| `--verbose` | `-v` | 详细输出模式 | ❌ |

## 📊 日志记录

程序会自动生成日志文件 `upload.log`，记录：
- 连接状态
- 上传进度
- 错误信息
- 访问链接

## ⚠️ 注意事项

1. **网络连接**：确保能够访问服务器 *************
2. **文件格式**：只支持 `.html` 和 `.htm` 文件
3. **权限设置**：上传的文件会自动设置为 644 权限
4. **目录权限**：远程目录会自动设置为 755 权限

## 🐛 故障排除

### 连接失败
```
SSH连接失败: [Errno 11001] getaddrinfo failed
```
**解决方案：**
- 检查网络连接
- 确认服务器地址正确
- 检查防火墙设置

### 权限错误
```
权限被拒绝
```
**解决方案：**
- 确认SSH用户名和密码正确
- 检查远程目录权限

### 文件不存在
```
本地文件不存在: xxx.html
```
**解决方案：**
- 检查文件路径是否正确
- 确认文件确实存在

## 📞 技术支持

如遇问题，请检查：
1. 日志文件 `upload.log`
2. 网络连接状态
3. 服务器访问权限

## 🔄 更新日志

### v1.0 (2025-08-01)
- 初始版本发布
- 支持SSH/SFTP上传
- 自动文件命名
- 批量上传功能
- 详细日志记录

---

**作者：** 项亮002  
**版本：** v1.0  
**更新时间：** 2025-08-01
