#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速上传脚本 - 简化版本
直接运行即可上传当前目录下的HTML文件
"""

import os
import sys
import glob
from html_uploader import HTMLUploader

def main():
    """快速上传当前目录下的HTML文件"""
    print("🚀 HTML快速上传工具")
    print("=" * 40)
    
    # 查找当前目录下的HTML文件
    html_files = glob.glob("*.html") + glob.glob("*.htm")
    
    if not html_files:
        print("❌ 当前目录下没有找到HTML文件")
        input("按回车键退出...")
        return
    
    print(f"📁 找到 {len(html_files)} 个HTML文件:")
    for i, file in enumerate(html_files, 1):
        print(f"  {i}. {file}")
    
    print("\n请选择操作:")
    print("1. 上传所有文件")
    print("2. 选择单个文件上传")
    print("3. 退出")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "3":
        return
    
    # 获取项目名称
    project_name = input("\n请输入项目名称 (直接回车使用默认): ").strip()
    if not project_name:
        project_name = "quick_upload"
    
    # 创建上传器
    uploader = HTMLUploader()
    
    try:
        # 建立连接
        print("\n🔗 正在连接服务器...")
        if not uploader.connect():
            print("❌ 无法连接到服务器")
            input("按回车键退出...")
            return
        
        if choice == "1":
            # 上传所有文件
            print(f"\n📤 开始批量上传 {len(html_files)} 个文件...")
            success_count = 0
            
            for file in html_files:
                print(f"\n正在上传: {file}")
                result = uploader.upload_file(file, project_name)
                if result:
                    success_count += 1
                    print(f"✅ 上传成功: {result['access_url']}")
                else:
                    print(f"❌ 上传失败: {file}")
            
            print(f"\n🎉 批量上传完成! 成功: {success_count}/{len(html_files)}")
            
        elif choice == "2":
            # 选择单个文件
            try:
                file_index = int(input(f"\n请选择文件编号 (1-{len(html_files)}): ")) - 1
                if 0 <= file_index < len(html_files):
                    selected_file = html_files[file_index]
                    print(f"\n📤 正在上传: {selected_file}")
                    
                    result = uploader.upload_file(selected_file, project_name)
                    if result:
                        print(f"\n✅ 上传成功!")
                        print(f"📁 文件名: {result['filename']}")
                        print(f"🌐 访问链接: {result['access_url']}")
                    else:
                        print("❌ 上传失败")
                else:
                    print("❌ 无效的文件编号")
            except ValueError:
                print("❌ 请输入有效的数字")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
    
    finally:
        uploader.disconnect()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
