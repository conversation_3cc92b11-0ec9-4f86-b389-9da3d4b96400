@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    HTML文件SSH上传工具 v1.0
echo    作者：项亮002
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.6+
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查是否有参数
if "%1"=="" (
    echo 📋 使用方法：
    echo.
    echo   上传单个文件：
    echo   upload.bat your_file.html
    echo.
    echo   指定项目名称：
    echo   upload.bat -p project_name your_file.html
    echo.
    echo   上传目录：
    echo   upload.bat your_directory\
    echo.
    echo   详细输出：
    echo   upload.bat -v your_file.html
    echo.
    pause
    exit /b 0
)

REM 检查依赖是否安装
python -c "import paramiko" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  检测到缺少依赖包，正在自动安装...
    echo.
    python install_requirements.py
    if errorlevel 1 (
        echo ❌ 依赖安装失败，请手动运行：pip install paramiko
        pause
        exit /b 1
    )
    echo.
)

REM 运行上传工具
echo 🚀 开始上传...
echo.
python html_uploader.py %*

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo ❌ 上传失败，请检查日志文件 upload.log
) else (
    echo.
    echo ✅ 操作完成！
)

echo.
pause
